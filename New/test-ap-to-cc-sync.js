/**
 * Test script for AP-to-CC custom field synchronization
 * 
 * This script will:
 * 1. Query the database for existing patients
 * 2. Test AP-to-CC sync for a patient with custom field data
 * 3. Monitor the sync process and log results
 */

const BASE_URL = 'http://localhost:8787';

async function queryPatients() {
    console.log('🔍 Querying database for existing patients...');
    
    try {
        // First, let's try to get some patient data by calling a simple endpoint
        const response = await fetch(`${BASE_URL}/admin/patients`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ Found patients:', data);
            return data;
        } else {
            console.log('❌ Failed to query patients:', response.status, response.statusText);
            return null;
        }
    } catch (error) {
        console.log('❌ Error querying patients:', error.message);
        return null;
    }
}

async function testApToCcSync(patientId) {
    console.log(`🚀 Testing AP-to-CC sync for patient: ${patientId}`);
    
    try {
        const response = await fetch(`${BASE_URL}/admin/custom-fields-sync/${patientId}/cc?skip=true`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            console.log('✅ AP-to-CC sync completed successfully:');
            console.log(JSON.stringify(data, null, 2));
            return data;
        } else {
            console.log('❌ AP-to-CC sync failed:');
            console.log('Status:', response.status, response.statusText);
            console.log('Response:', JSON.stringify(data, null, 2));
            return null;
        }
    } catch (error) {
        console.log('❌ Error during AP-to-CC sync:', error.message);
        return null;
    }
}

async function testCcToApSync(patientId) {
    console.log(`🔄 Testing CC-to-AP sync for comparison: ${patientId}`);
    
    try {
        const response = await fetch(`${BASE_URL}/admin/custom-fields-sync/${patientId}/ap?skip=true`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            console.log('✅ CC-to-AP sync completed successfully:');
            console.log(JSON.stringify(data, null, 2));
            return data;
        } else {
            console.log('❌ CC-to-AP sync failed:');
            console.log('Status:', response.status, response.statusText);
            console.log('Response:', JSON.stringify(data, null, 2));
            return null;
        }
    } catch (error) {
        console.log('❌ Error during CC-to-AP sync:', error.message);
        return null;
    }
}

async function main() {
    console.log('🧪 Starting AP-to-CC Custom Field Sync Test\n');
    
    // Step 1: Query for existing patients
    const patients = await queryPatients();
    
    if (!patients || !patients.length) {
        console.log('⚠️  No patients found. Let\'s try with a test patient ID...');
        
        // Try with some common test patient IDs
        const testPatientIds = [
            'test-patient-1',
            'patient-123',
            'demo-patient',
            '1',
            '2',
            '3'
        ];
        
        for (const testId of testPatientIds) {
            console.log(`\n🔍 Trying patient ID: ${testId}`);
            
            // Test AP-to-CC sync
            const apToCcResult = await testApToCcSync(testId);
            
            if (apToCcResult) {
                console.log(`\n✅ Found working patient ID: ${testId}`);
                
                // Also test CC-to-AP for comparison
                await testCcToApSync(testId);
                break;
            }
        }
    } else {
        // Use the first patient found
        const patientId = patients[0].id || patients[0];
        console.log(`\n🎯 Using patient ID: ${patientId}`);
        
        // Test both directions
        await testApToCcSync(patientId);
        await testCcToApSync(patientId);
    }
    
    console.log('\n🏁 Test completed!');
}

// Run the test
main().catch(console.error);
