/**
 * Direct database query test script
 * 
 * This script will create a simple endpoint to query the database directly
 * and create test patient records for testing AP-to-CC sync
 */

const BASE_URL = 'http://localhost:8787';

async function createTestPatient() {
    console.log('🏗️  Creating test patient record...');
    
    try {
        const testPatientData = {
            apId: "test-ap-contact-123",
            ccId: 1766, // Using the CC patient ID from the webhook sample
            email: "<EMAIL>",
            phone: "+**********",
            apData: {
                id: "test-ap-contact-123",
                firstName: "Test",
                lastName: "Patient",
                email: "<EMAIL>",
                customFields: [
                    {
                        id: "test-field-1",
                        value: "Test Value 1"
                    },
                    {
                        id: "test-field-2", 
                        value: "Value A, Value B, Value C" // TEXTBOX_LIST format
                    }
                ]
            },
            ccData: {
                id: 1766,
                firstName: "Test",
                lastName: "Patient",
                email: "<EMAIL>",
                customFields: [11315, 11316, 11317] // CC custom field IDs
            }
        };
        
        // We'll need to create an endpoint for this, but for now let's try a direct approach
        console.log('Test patient data prepared:', JSON.stringify(testPatientData, null, 2));
        return testPatientData;
        
    } catch (error) {
        console.log('❌ Error creating test patient:', error.message);
        return null;
    }
}

async function queryDatabase() {
    console.log('🔍 Attempting to query database directly...');
    
    try {
        // Try to call a simple endpoint that might give us database info
        const response = await fetch(`${BASE_URL}/admin/error-cleanup`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ Database connection working:', data);
            return true;
        } else {
            console.log('❌ Database query failed:', response.status, response.statusText);
            return false;
        }
    } catch (error) {
        console.log('❌ Error querying database:', error.message);
        return false;
    }
}

async function testWithRealPatientIds() {
    console.log('🔍 Testing with potential real patient IDs from webhook samples...');
    
    // From the webhook samples, we can see some real IDs
    const realPatientIds = [
        '1766', // From CC webhook sample
        'k1iaEbGJt6MJWXgHGRrt', // From AP webhook sample
    ];
    
    for (const patientId of realPatientIds) {
        console.log(`\n🧪 Testing AP-to-CC sync with ID: ${patientId}`);
        
        try {
            const response = await fetch(`${BASE_URL}/admin/custom-fields-sync/${patientId}/cc?skip=true`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            
            const data = await response.json();
            
            if (response.ok) {
                console.log('✅ AP-to-CC sync successful:');
                console.log(JSON.stringify(data, null, 2));
                return { success: true, patientId, data };
            } else {
                console.log('❌ AP-to-CC sync failed:');
                console.log('Status:', response.status);
                console.log('Response:', JSON.stringify(data, null, 2));
                
                // Check if it's a different error than "patient not found"
                if (data.data && data.data.errors && !data.data.errors.some(e => e.includes('Failed to fetch patient data'))) {
                    console.log('🔍 Different error detected - this might be progress!');
                    return { success: false, patientId, data, differentError: true };
                }
            }
        } catch (error) {
            console.log('❌ Error during sync test:', error.message);
        }
    }
    
    return null;
}

async function main() {
    console.log('🧪 Starting Database Query and AP-to-CC Sync Test\n');
    
    // Step 1: Test database connection
    const dbWorking = await queryDatabase();
    
    if (!dbWorking) {
        console.log('⚠️  Database connection issues detected');
    }
    
    // Step 2: Try with real patient IDs from webhook samples
    console.log('\n📋 Testing with real patient IDs from webhook samples...');
    const realIdResult = await testWithRealPatientIds();
    
    if (realIdResult && realIdResult.success) {
        console.log(`\n✅ Found working patient ID: ${realIdResult.patientId}`);
        
        // Also test CC-to-AP for comparison
        console.log('\n🔄 Testing CC-to-AP sync for comparison...');
        try {
            const ccToApResponse = await fetch(`${BASE_URL}/admin/custom-fields-sync/${realIdResult.patientId}/ap?skip=true`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            
            const ccToApData = await ccToApResponse.json();
            
            if (ccToApResponse.ok) {
                console.log('✅ CC-to-AP sync successful:');
                console.log(JSON.stringify(ccToApData, null, 2));
            } else {
                console.log('❌ CC-to-AP sync failed:');
                console.log(JSON.stringify(ccToApData, null, 2));
            }
        } catch (error) {
            console.log('❌ Error during CC-to-AP sync test:', error.message);
        }
    } else if (realIdResult && realIdResult.differentError) {
        console.log(`\n🔍 Found patient ID with different error: ${realIdResult.patientId}`);
        console.log('This suggests the patient lookup is working but there\'s another issue');
    } else {
        console.log('\n⚠️  No working patient IDs found');
    }
    
    // Step 3: Create test patient data structure
    console.log('\n📝 Preparing test patient data...');
    await createTestPatient();
    
    console.log('\n🏁 Test completed!');
    console.log('\n📋 Summary:');
    console.log('- Database connection:', dbWorking ? '✅ Working' : '❌ Issues');
    console.log('- Real patient ID test:', realIdResult ? (realIdResult.success ? '✅ Success' : '⚠️  Partial') : '❌ Failed');
}

// Run the test
main().catch(console.error);
